<script setup lang="ts">
defineOptions({
  name: 'TextForm',
})

interface Props {
  modelValue: {
    textContent: string
    questionType: string
    difficulty: string
    questionCount: number
    otherRequirements: string
  }
}

interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 题目类型选项
const questionTypeOptions = [
  { label: '选择题', value: 'choice' },
  { label: '填空题', value: 'blank' },
  { label: '判断题', value: 'judge' },
  { label: '简答题', value: 'short' },
  { label: '综合题', value: 'comprehensive' },
]

// 难度选项
const difficultyOptions = [
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' },
]

// 计算属性用于双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})
</script>

<template>
  <div>
    <!-- 文本内容 -->
    <NFormItem label="文本内容" path="textContent" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">文本内容</span>
      </template>
      <NInput
        v-model:value="formData.textContent"
        type="textarea"
        placeholder="请输入要出题的文本内容"
        :rows="6"
        clearable
        maxlength="2000"
        show-count
      />
    </NFormItem>

    <!-- 题目类型 -->
    <NFormItem label="题目类型" path="questionType" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">题目类型</span>
      </template>
      <NSelect
        v-model:value="formData.questionType"
        :options="questionTypeOptions"
        placeholder="请选择题目类型"
        clearable
      />
    </NFormItem>

    <!-- 难度等级 -->
    <NFormItem label="难度等级" path="difficulty" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">难度等级</span>
      </template>
      <NSelect
        v-model:value="formData.difficulty"
        :options="difficultyOptions"
        placeholder="请选择难度等级"
        clearable
      />
    </NFormItem>

    <!-- 题目数量 -->
    <NFormItem label="题目数量" path="questionCount" required>
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">题目数量</span>
      </template>
      <NInputNumber
        v-model:value="formData.questionCount"
        placeholder="请输入题目数量"
        :min="1"
        :max="50"
        class="w-full"
      />
    </NFormItem>

    <!-- 描述文本 -->
    <div class="mb-16px rounded-8px bg-green-50 p-12px">
      <p class="text-14px text-gray-600 leading-20px">
        <SvgIcon icon="mdi:information-outline" class="mr-4px inline text-green-500" />
        基于输入的文本内容，AI 会自动分析并生成相应类型和难度的题目
      </p>
    </div>

    <!-- 其他要求 -->
    <NFormItem label="其他要求" path="otherRequirements">
      <template #label>
        <span class="text-left text-14px text-[#464646] font-500">其他要求</span>
      </template>
      <NInput
        v-model:value="formData.otherRequirements"
        type="textarea"
        placeholder="请输入其他特殊要求（可选）"
        :rows="3"
        clearable
        maxlength="500"
        show-count
      />
    </NFormItem>
  </div>
</template>
